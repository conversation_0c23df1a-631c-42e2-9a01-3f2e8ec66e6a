
import React from 'react';
import SearchIcon from './icons/SearchIcon';

const Header: React.FC = () => {
    const navLinks = ["होम", "श्रेणियाँ", "लेखक", "हमारे बारे में"];

    return (
        <header className="bg-white/80 backdrop-blur-md shadow-sm sticky top-0 z-50">
            <div className="container mx-auto px-4">
                <div className="flex items-center justify-between h-20">
                    <div className="logo">
                        <h1 className="text-3xl font-bold text-rose-800 font-hindi">शायरी संसार</h1>
                    </div>

                    <nav className="hidden md:flex nav-menu">
                        <ul className="flex items-center space-x-8">
                            {navLinks.map((link) => (
                                <li key={link}>
                                    <a href="#" className="text-gray-600 hover:text-rose-700 transition-colors duration-300 font-medium">
                                        {link}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    </nav>

                    <div className="header-actions flex items-center gap-4">
                        <div className="relative hidden sm:block">
                            <input
                                type="text"
                                className="search-input bg-gray-100 border border-gray-200 rounded-full py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-rose-300 transition-all duration-300 w-40 md:w-56"
                                placeholder="शायरी खोजें..."
                            />
                            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                                <SearchIcon className="w-5 h-5" />
                            </div>
                        </div>
                        <button className="lang-toggle btn border border-rose-600 text-rose-600 hover:bg-rose-600 hover:text-white font-semibold py-2 px-4 rounded-full text-sm transition-colors duration-300">
                            हिं/En
                        </button>
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;
