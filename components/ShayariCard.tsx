
import React from 'react';
import type { <PERSON><PERSON> } from '../types';
import HeartIcon from './icons/HeartIcon';

interface ShayariCardProps {
    shayari: Shayari;
}

const ShayariCard: React.FC<ShayariCardProps> = ({ shayari }) => {
    return (
        <div className="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-shadow duration-300 overflow-hidden flex flex-col border border-gray-100 group">
            <div className="p-6 flex-grow">
                <h4 className="text-xl font-bold font-hindi text-gray-800 mb-2">{shayari.title}</h4>
                <p className="text-sm text-gray-500 mb-4">~ {shayari.author}</p>
                <div className="text-gray-700 space-y-2 font-hindi text-lg leading-relaxed mb-4">
                    {shayari.lines.map((line, index) => (
                        <p key={index}>{line}</p>
                    ))}
                </div>
            </div>
            <div className="px-6 pt-4 pb-6 bg-gray-50/70">
                <div className="flex justify-between items-center">
                    <div className="flex flex-wrap gap-2">
                        {shayari.tags.map((tag) => (
                            <span key={tag} className="bg-rose-100 text-rose-800 text-xs font-semibold px-2.5 py-1 rounded-full">
                                #{tag}
                            </span>
                        ))}
                    </div>
                    <div className="flex items-center gap-2 text-rose-500 group-hover:text-rose-600 transition-colors">
                        <HeartIcon className="w-5 h-5"/>
                        <span className="font-semibold text-sm">{shayari.likes}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ShayariCard;
