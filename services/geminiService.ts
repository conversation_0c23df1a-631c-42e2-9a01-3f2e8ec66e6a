
import { GoogleGenAI, Type } from "@google/genai";
import type { <PERSON><PERSON> } from '../types';

if (!process.env.API_KEY) {
  throw new Error("API_KEY environment variable is not set.");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

const responseSchema = {
  type: Type.ARRAY,
  items: {
    type: Type.OBJECT,
    properties: {
      title: {
        type: Type.STRING,
        description: "A creative and relevant title for the Hindi shayari.",
      },
      author: {
        type: Type.STRING,
        description: "A fictional, poetic-sounding Indian author name.",
      },
      lines: {
        type: Type.ARRAY,
        description: "The full shayari, with each line as a separate string in the array. Should be between 4 to 8 lines.",
        items: {
          type: Type.STRING,
        },
      },
      tags: {
        type: Type.ARRAY,
        description: "An array of 2-3 relevant tags in Hindi, like 'प्रेम', 'ज़िन्दगी', 'दर्द'.",
        items: {
          type: Type.STRING,
        },
      },
    },
    required: ["title", "author", "lines", "tags"],
  },
};

export const generateShayaris = async (count: number = 9): Promise<Shayari[]> => {
  try {
    const prompt = `Generate ${count} beautiful and profound Hindi shayaris on diverse topics like love, life, philosophy, nature, and relationships. Ensure variety in themes and moods. For each shayari, provide a title, a fictional author, the lines of the poem, and relevant tags. The author names should sound authentic and poetic. The shayaris should be emotionally resonant.`;

    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: responseSchema,
      },
    });

    const jsonText = response.text.trim();
    const generatedContent = JSON.parse(jsonText) as Omit<Shayari, 'likes'>[];

    // Add random likes to each shayari
    return generatedContent.map(shayari => ({
      ...shayari,
      likes: Math.floor(Math.random() * 500) + 50,
    }));

  } catch (error) {
    console.error("Error generating shayaris:", error);
    // In case of an API error, return some fallback data
    return getFallbackShayaris();
  }
};

const getFallbackShayaris = (): Shayari[] => {
  return [
    {
      title: "वक़्त का सफ़र",
      author: "अज्ञात",
      lines: [
        "वक़्त के पन्नों पर, यादों की स्याही है,",
        "कुछ लम्हे कैद हैं, कुछ की गवाही है।",
        "सफ़र ये ज़िन्दगी का, चलता ही जाएगा,",
        "हर मोड़ पर कोई, अपना मिल जाएगा।"
      ],
      tags: ["ज़िन्दगी", "वक़्त"],
      likes: 258
    },
    {
      title: "दिल की आवाज़",
      author: "कल्पना",
      lines: [
        "खामोशियों में भी, एक आवाज़ होती है,",
        "दिल की हर धड़कन, एक राज़ होती है।",
        "समझ सको तो पढ़ लो, इन आँखों की गहराई,",
        "हर नज़र में एक, पूरी किताब होती है।"
      ],
      tags: ["प्रेम", "खामोशी"],
      likes: 412
    },
     {
      title: "उम्मीद की किरण",
      author: "साहिल",
      lines: [
        "अंधेरों से न घबराना, ऐ दिल मेरे,",
        "हर रात के बाद, सुबह होती है।",
        "उम्मीद का दामन, थामे रखना,",
        "मंज़िल उन्हीं को मिलती है, जिनमें जान होती है।"
      ],
      tags: ["प्रेरणा", "उम्मीद"],
      likes: 350
    }
  ];
};
