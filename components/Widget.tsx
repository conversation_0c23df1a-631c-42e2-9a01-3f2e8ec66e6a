
import React from 'react';

interface WidgetProps {
    title: string;
    children: React.ReactNode;
}

const Widget: React.FC<WidgetProps> = ({ title, children }) => {
    return (
        <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100">
            <h4 className="text-xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-rose-200 font-hindi">
                {title}
            </h4>
            {children}
        </div>
    );
};

export default Widget;
