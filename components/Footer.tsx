
import React from 'react';

const Footer: React.FC = () => {
    const sections = {
        "श्रेणियाँ": ["प्रेम शायरी", "दुख शायरी", "मोटिवेशनल शायरी", "दोस्ती शायरी"],
        "लेखक": ["राहुल शर्मा", "प्रिया गुप्ता", "अमित कुमार", "सुनीता देवी"],
        "About": ["हमारे बारे में", "Contact", "Privacy Policy", "Terms of Service"],
    };

    return (
        <footer className="bg-gray-800 text-gray-300 mt-16">
            <div className="container mx-auto px-4 py-12">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                    {Object.entries(sections).map(([title, links]) => (
                        <div key={title} className="footer-section">
                            <h4 className="text-lg font-bold text-white mb-4 font-hindi">{title}</h4>
                            <ul className="space-y-2">
                                {links.map(link => (
                                    <li key={link}>
                                        <a href="#" className="hover:text-rose-300 transition-colors">{link}</a>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                    <div className="footer-section">
                        <h4 className="text-lg font-bold text-white mb-4 font-hindi">Follow Us</h4>
                        <div className="flex flex-col space-y-2">
                           <a href="#" className="hover:text-rose-300 transition-colors">📘 Facebook</a>
                           <a href="#" className="hover:text-rose-300 transition-colors">🐦 Twitter</a>
                           <a href="#" className="hover:text-rose-300 transition-colors">📷 Instagram</a>
                        </div>
                    </div>
                </div>

                <div className="mt-12 border-t border-gray-700 pt-8 text-center text-gray-400 text-sm">
                    <p>&copy; {new Date().getFullYear()} शायरी संसार. All rights reserved.</p>
                    <p>Designed with ❤️ for the love of poetry.</p>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
