
import React from 'react';

const Hero: React.FC = () => {
    return (
        <section className="hero bg-rose-50/50 py-12 sm:py-20">
            <div className="container mx-auto px-4">
                <div className="relative rounded-2xl overflow-hidden shadow-xl min-h-[400px] flex items-center">
                    <img 
                        src="https://picsum.photos/1200/500?random=1&blur=2" 
                        alt="Inspirational background" 
                        className="absolute inset-0 w-full h-full object-cover" 
                    />
                    <div className="absolute inset-0 bg-black/40"></div>
                    <div className="relative text-center mx-auto text-white p-6">
                        <h2 className="text-4xl md:text-6xl font-bold font-hindi mb-4 drop-shadow-lg">आज की खास शायरी</h2>
                        <p className="text-lg md:text-xl max-w-2xl mx-auto drop-shadow-md">
                            प्रेम, दुख, और ज़िन्दगी की गहराइयों से निकली अनमोल शायरियाँ
                        </p>
                        <button className="mt-8 bg-rose-600 text-white font-bold py-3 px-8 rounded-full hover:bg-rose-700 transition-transform duration-300 hover:scale-105 shadow-lg">
                            और पढ़ें
                        </button>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Hero;
