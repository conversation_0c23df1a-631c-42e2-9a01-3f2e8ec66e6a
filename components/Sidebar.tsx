
import React from 'react';
import Widget from './Widget';
import HeartIcon from './icons/HeartIcon';

const Sidebar: React.FC = () => {
    const popularShayari = [
        { title: "दिल की आवाज़", author: "राहुल शर्मा", likes: 456 },
        { title: "मोहब्बत का एहसास", author: "प्रिया गुप्ता", likes: 389 },
        { title: "जिंदगी के रंग", author: "अमित कुमार", likes: 298 },
    ];

    const categories = ["प्रेम शायरी", "दर्द भरी शायरी", "प्रेरणादायक शायरी", "दोस्ती शायरी", "जीवन दर्शन"];

    return (
        <aside className="lg:w-1/3 flex-shrink-0 space-y-8">
            <Widget title="लोकप्रिय शायरी">
                <div className="space-y-4">
                    {popularShayari.map((item, index) => (
                        <div key={index} className="popular-item">
                            <h5 className="font-bold text-gray-700 font-hindi">{item.title}</h5>
                            <div className="flex justify-between items-center text-sm text-gray-500 mt-1">
                                <p>~ {item.author}</p>
                                <span className="flex items-center gap-1 text-red-500">
                                    {item.likes} <HeartIcon className="w-4 h-4" />
                                </span>
                            </div>
                        </div>
                    ))}
                </div>
            </Widget>

            <Widget title="श्रेणियाँ">
                <div className="flex flex-wrap gap-2">
                    {categories.map(cat => (
                         <a href="#" key={cat} className="bg-gray-200 text-gray-700 text-sm font-medium px-3 py-1.5 rounded-lg hover:bg-rose-200 hover:text-rose-800 transition-colors">
                            {cat}
                         </a>
                    ))}
                </div>
            </Widget>
            
            <Widget title="लेखक Spotlight">
                <div className="flex flex-col items-center text-center">
                    <img src="https://picsum.photos/id/237/100/100" alt="Author Spotlight" className="w-24 h-24 rounded-full mb-4 object-cover shadow-md"/>
                    <h5 className="font-bold text-lg text-gray-800">राहुल शर्मा</h5>
                    <p className="text-gray-600 text-sm mt-1">प्रेम और जिंदगी की शायरी के मशहूर लेखक।</p>
                    <button className="mt-4 text-sm font-semibold text-rose-600 border border-rose-600 px-4 py-2 rounded-full hover:bg-rose-600 hover:text-white transition-colors">
                        प्रोफ़ाइल देखें
                    </button>
                </div>
            </Widget>

            <Widget title="न्यूज़लेटर">
                <p className="text-gray-600 mb-4">रोज़ाना नई शायरी अपने इनबॉक्स में पाएं।</p>
                <div className="newsletter-signup flex flex-col gap-3">
                    <input type="email" className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-rose-400" placeholder="आपका ईमेल" />
                    <button className="w-full bg-rose-600 text-white font-bold py-2.5 px-4 rounded-lg hover:bg-rose-700 transition-colors">
                        सब्सक्राइब करें
                    </button>
                </div>
            </Widget>
        </aside>
    );
};

export default Sidebar;
